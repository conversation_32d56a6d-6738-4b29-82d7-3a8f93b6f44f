<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\RoleController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\SettingController;
use App\Http\Controllers\LeadController;
use App\Http\Controllers\QuotationController;
use App\Http\Controllers\OrderController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\TaskController;
use App\Http\Controllers\NotificationController;
use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use Illuminate\Foundation\Http\Middleware\HandlePrecognitiveRequests;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('/', function () {
    return Inertia::render('Auth/Login', [
        'canLogin'          => Route::has('login'),
        'canRegister'       => Route::has('register'),
        'laravelVersion'    => Application::VERSION,
        'phpVersion'        => PHP_VERSION,
    ]);
});

Route::get('/dashboard',      [DashboardController::class, 'dashboard'])->middleware(['auth', 'verified'])->name('dashboard');

Route::middleware('auth')->group(function () {

    Route::get('/profile',      [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile',    [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile',   [ProfileController::class, 'destroy'])->name('profile.destroy');

    //Roles
    Route::resource('roles', RoleController::class)->middleware([HandlePrecognitiveRequests::class]);

    //Users
    Route::resource('users', UserController::class)->middleware([HandlePrecognitiveRequests::class]);
    Route::patch('/users',              [UserController::class, 'update'])->middleware([HandlePrecognitiveRequests::class])->name('users.update');
    Route::post('/users/activation',    [UserController::class, 'activation'])->name('users.activation');
    Route::get('export-users', [UserController::class, 'exportExcel']);

    //Leads
    Route::resource('leads',           LeadController::class)->middleware([HandlePrecognitiveRequests::class]);
    Route::patch('/leads',             [LeadController::class, 'update'])->middleware([HandlePrecognitiveRequests::class])->name('leads.update');
    Route::post('/leads/status/{id}', [LeadController::class, 'updateStatus'])->name('leads.update-status');
    Route::post('/leads/{id}/comments', [LeadController::class, 'addComment'])->name('leads.add-comment');
    Route::put('/leads/comments/{commentId}', [LeadController::class, 'updateComment'])->name('leads.update-comment');
    Route::delete('/leads/comments/{commentId}', [LeadController::class, 'deleteComment'])->name('leads.delete-comment');
    Route::get('/removedocument/{id}', [LeadController::class, 'removedocument'])->name('removedocument');

    //Quotations
    Route::resource('quotations',      QuotationController::class)->middleware([HandlePrecognitiveRequests::class]);
    Route::patch('/quotations',        [QuotationController::class, 'update'])->middleware([HandlePrecognitiveRequests::class])->name('quotations.update');
    Route::post('/quotations/status/{id}', [QuotationController::class, 'updateStatus'])->name('quotations.update-status');
    Route::get('/quotations/{quotation}/pdf', [QuotationController::class, 'generatePdf'])->name('quotations.pdf');
    Route::get('/quotations/convert/{lead}', [QuotationController::class, 'convertFromLead'])->name('quotations.convert');
    Route::get('/removequotationdocument/{id}', [QuotationController::class, 'removedocument'])->name('removequotationdocument');

    //Orders
    Route::resource('orders',          OrderController::class)->middleware([HandlePrecognitiveRequests::class]);
    Route::post('/orders/status/{id}', [OrderController::class, 'updateStatus'])->name('orders.update-status');

    Route::get('/orders/{order}/pdf', [OrderController::class, 'generatePdf'])->name('orders.pdf');
    Route::get('/orders/convert/{quotation}', [OrderController::class, 'convertFromQuotation'])->name('orders.convert');

    //Reports
    Route::get('/setting',          [SettingController::class, 'index'])->name('setting');
    Route::get('logs',              [SettingController::class, 'getAllLogs'])->name('logs');
    Route::get('/logs/load-more',   [SettingController::class, 'loadMoreLogs'])->name('logs.loadMore');

    //Tasks
    Route::resource('tasks', TaskController::class)->middleware([HandlePrecognitiveRequests::class]);
    Route::post('/tasks/{task}/complete', [TaskController::class, 'complete'])->name('tasks.complete');
    Route::get('/tasks-dashboard', [TaskController::class, 'dashboard'])->name('tasks.dashboard');

    //Notifications
    Route::get('/notifications', [NotificationController::class, 'index'])->name('notifications.index');
    Route::post('/notifications/{id}/read', [NotificationController::class, 'markAsRead'])->name('notifications.read');
    Route::post('/notifications/read-all', [NotificationController::class, 'markAllAsRead'])->name('notifications.read-all');
    Route::get('/notifications/unread-count', [NotificationController::class, 'getUnreadCount'])->name('notifications.unread-count');
    Route::get('/notifications/recent', [NotificationController::class, 'getRecent'])->name('notifications.recent');
    Route::delete('/notifications/{notification}', [NotificationController::class, 'destroy'])->name('notifications.destroy');

});


require __DIR__ . '/auth.php';



